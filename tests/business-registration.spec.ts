import { test, expect } from '@playwright/test';

test.describe('Business Registration', () => {
  test('should successfully register a new business', async ({ page }) => {
    // Navigate to business registration page
    await page.goto('http://localhost:3005/register/business');
    
    // Wait for page to load
    await expect(page.locator('h1')).toContainText('Business Registration');
    
    // Fill out Step 1: Company Information
    await page.fill('input[name="company_name"]', 'Test Company Inc');
    await page.selectOption('select[name="legal_entity_type"]', 'Corporation');
    await page.fill('input[name="tax_id"]', '12-3456789');
    await page.selectOption('select[name="business_type"]', 'staffing_agency');
    await page.selectOption('select[name="estimated_annual_volume"]', '$1M - $5M');
    
    // Click Next to go to Step 2
    await page.click('button:has-text("Next")');
    
    // Fill out Step 2: Business Address
    await page.fill('input[name="address_line1"]', '123 Business St');
    await page.fill('input[name="city"]', 'Business City');
    await page.fill('input[name="state"]', 'CA');
    await page.fill('input[name="postal_code"]', '90210');
    await page.selectOption('select[name="country"]', 'United States');
    
    // Click Next to go to Step 3
    await page.click('button:has-text("Next")');
    
    // Fill out Step 3: Primary Contact
    await page.fill('input[name="first_name"]', 'John');
    await page.fill('input[name="last_name"]', 'Doe');
    await page.fill('input[name="email"]', `test-${Date.now()}@testcompany.com`);
    await page.fill('input[name="phone"]', '******-123-4567');
    await page.fill('input[name="job_title"]', 'CEO');
    
    // Click Next to go to Step 4
    await page.click('button:has-text("Next")');
    
    // Fill out Step 4: Account Setup
    await page.fill('input[name="password"]', 'TestPassword123!');
    await page.fill('input[name="confirm_password"]', 'TestPassword123!');
    
    // Check process permissions
    await page.check('input[name="recruitment_enabled"]');
    await page.check('input[name="bench_sales_enabled"]');
    
    // Set working hours (optional - use defaults)
    
    // Submit the form
    await page.click('button[type="submit"]:has-text("Complete Registration")');
    
    // Wait for success or redirect
    await page.waitForTimeout(3000);
    
    // Check for success - should redirect to documents page or show success message
    const currentUrl = page.url();
    const hasSuccessMessage = await page.locator('.bg-green-50, .text-green-800').count() > 0;
    const isDocumentsPage = currentUrl.includes('/documents');
    
    // Verify registration was successful
    expect(hasSuccessMessage || isDocumentsPage).toBeTruthy();
    
    // If redirected to documents page, verify we're there
    if (isDocumentsPage) {
      await expect(page.locator('h1')).toContainText('Business Documents');
    }
    
    console.log('Business registration completed successfully!');
    console.log('Current URL:', currentUrl);
  });
  
  test('should show validation errors for invalid data', async ({ page }) => {
    await page.goto('http://localhost:3005/register/business');
    
    // Try to submit without filling required fields
    await page.click('button:has-text("Next")');
    
    // Should show validation errors
    const errorMessages = await page.locator('.text-red-500, .border-red-500').count();
    expect(errorMessages).toBeGreaterThan(0);
  });
  
  test('should validate email format', async ({ page }) => {
    await page.goto('http://localhost:3005/register/business');
    
    // Fill minimal required fields to get to contact step
    await page.fill('input[name="company_name"]', 'Test Company');
    await page.selectOption('select[name="legal_entity_type"]', 'Corporation');
    await page.fill('input[name="tax_id"]', '12-3456789');
    await page.click('button:has-text("Next")');
    
    // Skip address step
    await page.fill('input[name="address_line1"]', '123 Test St');
    await page.fill('input[name="city"]', 'Test City');
    await page.fill('input[name="state"]', 'CA');
    await page.fill('input[name="postal_code"]', '90210');
    await page.click('button:has-text("Next")');
    
    // Enter invalid email
    await page.fill('input[name="email"]', 'invalid-email');
    await page.click('button:has-text("Next")');
    
    // Should show email validation error
    const emailError = await page.locator('text=valid email').count();
    expect(emailError).toBeGreaterThan(0);
  });
});
