import { fail, redirect } from '@sveltejs/kit'
import { createSupabaseServerClient } from '$lib/supabase'

export const load = async ({ url }) => {
  const error = url.searchParams.get('error')
  let errorMessage = null
  
  if (error === 'confirmation_failed') {
    errorMessage = 'Email confirmation failed. Please try registering again or contact support.'
  }
  
  return {
    error: errorMessage
  }
}

export const actions = {
  login: async ({ request, cookies }) => {
    const supabase = createSupabaseServerClient(cookies)

    const formData = await request.formData()
    const email = formData.get('email') as string
    const password = formData.get('password') as string

    if (!email || !password) {
      return fail(400, {
        error: 'Email and password are required',
        email
      })
    }

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error) {
      console.error('Auth error:', error)
      return fail(400, {
        error: 'Invalid email or password',
        email
      })
    }

    // Check if user has a candidate profile
    const { data: candidateData } = await supabase
      .from('candidates')
      .select('id')
      .eq('auth_user_id', data.user.id) // Fixed: use auth_user_id instead of user_id
      .single()

    if (!candidateData) {
      // Redirect to profile creation if no candidate profile exists
      throw redirect(302, '/complete-profile')
    }

    throw redirect(302, '/dashboard')
  }
}
