<script lang="ts">
  import { enhance } from '$app/forms'
  import { Eye, EyeOff, UserCheck, AlertCircle } from 'lucide-svelte'
  import Button from '$lib/components/ui/button.svelte'
  import Input from '$lib/components/ui/input.svelte'
  import Label from '$lib/components/ui/label.svelte'
  import Card from '$lib/components/ui/card.svelte'
  import CardHeader from '$lib/components/ui/card-header.svelte'
  import CardContent from '$lib/components/ui/card-content.svelte'
  import CardTitle from '$lib/components/ui/card-title.svelte'
  import type { ActionData } from './$types'

  let { form, data }: { form: ActionData, data: any } = $props()

  let showPassword = $state(false)
  let loading = $state(false)
  let email = $state('')
  let password = $state('')

  function togglePasswordVisibility() {
    showPassword = !showPassword
  }
</script>

<svelte:head>
  <title>Sign In - Candidate Portal</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-background px-4">
  <div class="max-w-md w-full space-y-8">
    <!-- Header -->
    <div class="text-center">
      <div class="mx-auto w-12 h-12 bg-primary rounded-lg flex items-center justify-center mb-4">
        <UserCheck class="w-6 h-6 text-primary-foreground" />
      </div>
      <h2 class="text-3xl font-bold text-foreground">Welcome Back</h2>
      <p class="mt-2 text-sm text-muted-foreground">
        Sign in to your candidate account
      </p>
    </div>

    <!-- Error Message -->
    {#if form?.error || data?.error}
      <div class="rounded-md bg-red-50 p-4 border border-red-200">
        <div class="flex">
          <AlertCircle class="h-5 w-5 text-red-400" />
          <div class="ml-3">
            <div class="text-sm text-red-800">
              {form?.error || data?.error}
            </div>
          </div>
        </div>
      </div>
    {/if}

    <!-- Login Form -->
    <Card>
      <CardHeader>
        <CardTitle>Sign In</CardTitle>
      </CardHeader>
      <CardContent>
        <form
          method="POST"
          action="?/login"
          use:enhance={() => {
            loading = true
            return async ({ update }) => {
              loading = false
              await update()
            }
          }}
          class="space-y-6"
        >
          <div class="space-y-4">
            <!-- Email -->
            <div class="space-y-2">
              <Label for="email">Email Address</Label>
              <Input
                id="email"
                name="email"
                type="email"
                autocomplete="email"
                required
                bind:value={email}
                placeholder="Enter your email"
                disabled={loading}
              />
            </div>

            <!-- Password -->
            <div class="space-y-2">
              <Label for="password">Password</Label>
              <div class="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autocomplete="current-password"
                  required
                  bind:value={password}
                  placeholder="Enter your password"
                  disabled={loading}
                  class="pr-10"
                />
                <button
                  type="button"
                  class="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onclick={togglePasswordVisibility}
                  disabled={loading}
                >
                  {#if showPassword}
                    <EyeOff class="h-4 w-4 text-muted-foreground" />
                  {:else}
                    <Eye class="h-4 w-4 text-muted-foreground" />
                  {/if}
                </button>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <Button
            type="submit"
            disabled={loading || !email || !password}
            class="w-full"
          >
            {#if loading}
              <div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
              Signing in...
            {:else}
              Sign In
            {/if}
          </Button>
        </form>
      </CardContent>
    </Card>

    <!-- Footer Links -->
    <div class="text-center space-y-4">
      <p class="text-sm text-muted-foreground">
        Don't have an account?
        <a href="/register" class="font-medium text-primary hover:text-primary/80">
          Create one here
        </a>
      </p>

      <p class="text-sm text-muted-foreground">
        <a href="/forgot-password" class="font-medium text-primary hover:text-primary/80">
          Forgot your password?
        </a>
      </p>

      <p class="text-xs text-muted-foreground">
        Looking for business portal?
        <a href="https://app.procureserve.com" class="text-primary hover:text-primary/80">
          Click here
        </a>
      </p>
    </div>
  </div>
</div>
