import { fail } from '@sveltejs/kit'
import { createSupabaseServerClient } from '$lib/supabase'
import type { Actions } from './$types'

export const actions: Actions = {
  register: async ({ request, cookies, url }) => {
    const formData = await request.formData()
    const email = formData.get('email') as string
    const password = formData.get('password') as string
    const confirmPassword = formData.get('confirm_password') as string
    const firstName = formData.get('first_name') as string
    const lastName = formData.get('last_name') as string
    const phone = formData.get('phone') as string

    // Validation
    if (!email || !password || !firstName || !lastName) {
      return fail(400, {
        error: 'Please fill in all required fields'
      })
    }

    if (password !== confirmPassword) {
      return fail(400, {
        error: 'Passwords do not match'
      })
    }

    if (password.length < 6) {
      return fail(400, {
        error: 'Password must be at least 6 characters long'
      })
    }

    const supabase = createSupabaseServerClient(cookies)

    try {
      // Create auth user with proper email confirmation redirect
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: firstName,
            last_name: lastName,
            user_type: 'candidate'
          },
          emailRedirectTo: `${url.origin}/auth/confirm`
        }
      })

      if (authError) {
        console.error('Auth error:', authError)
        return fail(400, {
          error: authError.message
        })
      }

      if (!authData.user) {
        return fail(400, {
          error: 'Failed to create user account'
        })
      }

      // Only create candidate profile if email is confirmed OR if confirmEmailChange is disabled
      // For email confirmation enabled, we'll create the profile after confirmation
      const fullName = `${firstName} ${lastName}`.trim()
      
      try {
        const { error: profileError } = await supabase
          .from('candidates')
          .insert({
            auth_user_id: authData.user.id, // Fixed: use auth_user_id instead of user_id
            first_name: firstName,
            last_name: lastName,
            name: fullName,
            email,
            phone: phone || null,
            status: 'active',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })

        if (profileError) {
          console.error('Profile creation error:', profileError)
          // Note: Can't use admin.deleteUser with anon key, so we'll let the user try again
          // The auth user will exist but without a profile, which we can handle in the login flow
          return fail(400, {
            error: 'Failed to create candidate profile. Please contact support if this persists.'
          })
        }
      } catch (profileErr) {
        console.error('Profile creation exception:', profileErr)
        // Same as above - let user try again rather than failing completely
        return fail(400, {
          error: 'Failed to create candidate profile. Please contact support if this persists.'
        })
      }

      // Return success state instead of redirecting
      // User needs to check email for verification
      return {
        success: true,
        message: 'Registration successful! Please check your email to verify your account before signing in.',
        email: email
      }

    } catch (error) {
      console.error('Registration error:', error)
      return fail(500, {
        error: 'An unexpected error occurred. Please try again.'
      })
    }
  }
}
