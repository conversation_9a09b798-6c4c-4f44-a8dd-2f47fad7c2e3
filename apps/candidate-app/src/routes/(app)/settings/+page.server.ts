import { createSupabaseServerClient } from '$lib/supabase'
import { redirect } from '@sveltejs/kit'
import type { PageServerLoad } from './$types'

export const load: PageServerLoad = async ({ cookies }) => {
  const supabase = createSupabaseServerClient(cookies)

  const { data: { session } } = await supabase.auth.getSession()

  if (!session?.user) {
    throw redirect(303, '/login')
  }

  // Get candidate profile for settings
  const { data: candidateProfile, error } = await supabase
    .from('candidates')
    .select('*')
    .eq('auth_user_id', session.user.id)
    .single()

  if (error) {
    console.error('Error fetching candidate profile:', error)
    // If no profile exists, redirect to complete profile
    if (error.code === 'PGRST116') { // No rows returned
      throw redirect(303, '/complete-profile')
    }
  }

  return {
    candidateProfile,
    user: session.user
  }
}
