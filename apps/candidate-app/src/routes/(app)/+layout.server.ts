import { createSupabaseServerClient } from '$lib/supabase'
import { redirect } from '@sveltejs/kit'

export const load = async ({ cookies, url }) => {
  const supabase = createSupabaseServerClient(cookies)

  const { data: { session } } = await supabase.auth.getSession()

  // Redirect unauthenticated users to login
  if (!session) {
    throw redirect(302, '/login')
  }

  // Get candidate profile if user is authenticated
  let candidateProfile = null
  if (session.user) {
    const { data, error } = await supabase
      .from('candidates')
      .select('*')
      .eq('auth_user_id', session.user.id)
      .single()

    candidateProfile = data

    // If no profile exists and not on complete-profile page, redirect there
    if (error && error.code === 'PGRST116' && url.pathname !== '/complete-profile') {
      throw redirect(302, '/complete-profile')
    }
  }

  return {
    session,
    user: session.user,
    candidateProfile,
    pathname: url.pathname
  }
}
