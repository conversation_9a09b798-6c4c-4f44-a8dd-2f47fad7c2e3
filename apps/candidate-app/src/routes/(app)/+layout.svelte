<script lang="ts">
  import CandidateAppLayout from '$lib/components/layout/CandidateAppLayout.svelte'
  import type { LayoutData } from './$types'

  export let data: LayoutData

  // Generate page title from route
  $: pageTitle = generatePageTitle(data.pathname)

  function generatePageTitle(pathname: string): string {
    const segments = pathname.split('/').filter(Boolean)
    if (segments.length === 0) return 'Dashboard'

    const titleMap: Record<string, string> = {
      'dashboard': 'Dashboard',
      'jobs': 'Job Search',
      'applications': 'My Applications',
      'profile': 'My Profile',
      'settings': 'Settings'
    }

    return titleMap[segments[segments.length - 1]] || segments[segments.length - 1]
  }
</script>

<CandidateAppLayout user={data.user} candidateProfile={data.candidateProfile} {pageTitle} pathname={data.pathname}>
  <slot />
</CandidateAppLayout>
