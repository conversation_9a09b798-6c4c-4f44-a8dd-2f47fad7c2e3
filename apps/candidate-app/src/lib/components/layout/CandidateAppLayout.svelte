<script lang="ts">
  import { Bell } from 'lucide-svelte'
  import { onMount } from 'svelte'

  import { SidebarProvider, SidebarTrigger } from '$lib/components/ui/sidebar'
  import CandidateSidebar from './CandidateSidebar.svelte'

  export let user: any = null
  export let candidateProfile: any = null
  export let pageTitle: string = 'Dashboard'
  export let pathname: string = '/dashboard'

  let isMobile = false
  let defaultOpen = true

  onMount(() => {
    const checkMobile = () => {
      isMobile = window.innerWidth < 1024
      defaultOpen = !isMobile
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)

    return () => {
      window.removeEventListener('resize', checkMobile)
    }
  })
</script>

<SidebarProvider defaultOpen={!isMobile}>
  <div class="flex min-h-screen w-full bg-background">
    <!-- Sidebar -->
    <CandidateSidebar {user} {candidateProfile} {pathname} />
    
    <!-- Main Content -->
    <div class="flex flex-1 flex-col overflow-hidden lg:ml-0">
      <!-- Top Header -->
      <header class="sticky top-0 z-30 flex h-14 items-center gap-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 px-4 lg:px-6">
        <SidebarTrigger class="lg:hidden" />

        <div class="flex-1 min-w-0">
          <h1 class="text-lg font-semibold text-foreground lg:text-xl truncate">{pageTitle}</h1>
        </div>

        <!-- Header actions -->
        <div class="flex items-center gap-2">
          <button
            class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-9 w-9"
            title="Notifications"
          >
            <Bell class="h-4 w-4" />
            <span class="sr-only">Notifications</span>
          </button>
        </div>
      </header>

      <!-- Main content area -->
      <main class="flex-1 overflow-auto p-4 lg:p-6">
        <div class="mx-auto max-w-7xl">
          <slot />
        </div>
      </main>
    </div>
  </div>
</SidebarProvider>
