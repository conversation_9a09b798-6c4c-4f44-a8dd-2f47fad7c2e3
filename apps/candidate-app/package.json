{"name": "candidate-app", "version": "0.0.1", "private": true, "scripts": {"build": "vite build", "dev": "vite dev --port 3006", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@sveltejs/adapter-vercel": "^5.0.0", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^4.0.0", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "vite": "^5.0.3"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.5.1", "@supabase/supabase-js": "^2.45.4", "@sveltejs/adapter-auto": "^6.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^2.30.0", "lucide-react": "^0.517.0", "lucide-svelte": "^0.445.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.63"}, "type": "module"}