// Supabase client configuration for ProcureServe II Customer App
import { createBrowserClient, createServerClient, isBrowser } from '@supabase/ssr'
import { PUBLIC_SUPABASE_ANON_KEY, PUBLIC_SUPABASE_URL } from '$env/static/public'

// App-specific session configuration for security isolation
const CUSTOMER_APP_SESSION_PREFIX = 'psii-customer'

export const createSupabaseLoadClient = (fetch: any, event: any) => {
  if (isBrowser()) {
    return createBrowserClient(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
      global: { fetch },
      auth: {
        flowType: 'pkce',
        persistSession: true,
        storage: {
          // App-specific session storage to prevent cross-app contamination
          getItem: (key: string) => {
            if (typeof window !== 'undefined') {
              return window.localStorage.getItem(`${CUSTOMER_APP_SESSION_PREFIX}.${key}`)
            }
            return null
          },
          setItem: (key: string, value: string) => {
            if (typeof window !== 'undefined') {
              window.localStorage.setItem(`${CUSTOMER_APP_SESSION_PREFIX}.${key}`, value)
            }
          },
          removeItem: (key: string) => {
            if (typeof window !== 'undefined') {
              window.localStorage.removeItem(`${CUSTOMER_APP_SESSION_PREFIX}.${key}`)
            }
          }
        }
      }
    })
  }

  return createServerClient(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
    global: { fetch },
    cookies: {
      getAll() {
        const allCookies = event.cookies.getAll() || []
        // Only return cookies for this app
        return allCookies.filter(cookie => 
          cookie.name.startsWith(`sb-${CUSTOMER_APP_SESSION_PREFIX}-`) ||
          cookie.name.startsWith('sb-') && !cookie.name.includes('console') && !cookie.name.includes('candidate')
        )
      },
      setAll(cookiesToSet) {
        cookiesToSet.forEach(({ name, value, options }) => {
          // Prefix cookie names to prevent cross-app interference
          const appSpecificName = name.startsWith('sb-') 
            ? name.replace('sb-', `sb-${CUSTOMER_APP_SESSION_PREFIX}-`)
            : name
          
          event.cookies.set(appSpecificName, value, { 
            ...options, 
            path: '/',
            sameSite: 'lax',
            secure: true,
            httpOnly: options?.httpOnly ?? false
          })
        })
      }
    }
  })
}

export const createSupabaseBrowserClient = () => {
  return createBrowserClient(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
    auth: {
      flowType: 'pkce',
      persistSession: true,
      storage: {
        // App-specific session storage
        getItem: (key: string) => {
          if (typeof window !== 'undefined') {
            return window.localStorage.getItem(`${CUSTOMER_APP_SESSION_PREFIX}.${key}`)
          }
          return null
        },
        setItem: (key: string, value: string) => {
          if (typeof window !== 'undefined') {
            window.localStorage.setItem(`${CUSTOMER_APP_SESSION_PREFIX}.${key}`, value)
          }
        },
        removeItem: (key: string) => {
          if (typeof window !== 'undefined') {
            window.localStorage.removeItem(`${CUSTOMER_APP_SESSION_PREFIX}.${key}`)
          }
        }
      }
    }
  })
}

// Export a browser client instance for use in components with app-specific isolation
export const supabase = createBrowserClient(PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY, {
  auth: {
    flowType: 'pkce',
    persistSession: true,
    storage: {
      getItem: (key: string) => {
        if (typeof window !== 'undefined') {
          return window.localStorage.getItem(`${CUSTOMER_APP_SESSION_PREFIX}.${key}`)
        }
        return null
      },
      setItem: (key: string, value: string) => {
        if (typeof window !== 'undefined') {
          window.localStorage.setItem(`${CUSTOMER_APP_SESSION_PREFIX}.${key}`, value)
        }
      },
      removeItem: (key: string) => {
        if (typeof window !== 'undefined') {
          window.localStorage.removeItem(`${CUSTOMER_APP_SESSION_PREFIX}.${key}`)
        }
      }
    }
  }
})
