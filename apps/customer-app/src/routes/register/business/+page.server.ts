import { redirect, fail } from '@sveltejs/kit'
import type { PageServerLoad, Actions } from './$types'
import { createSupabaseServiceClient } from '$lib/supabase-service'

export const load: PageServerLoad = async ({ locals }) => {
  // Check if user is already logged in - if so, redirect appropriately
  const { data: { session } } = await locals.supabase.auth.getSession()
  
  if (session) {
    // Check if they're already a business user
    const { data: existingUser } = await locals.supabase
      .from('users')
      .select('company_id, companies(registration_status)')
      .eq('id', session.user.id)
      .single()
    
    if (existingUser?.company_id) {
      // Already has a company, redirect to appropriate page based on status
      const status = existingUser.companies?.registration_status
      if (status === 'approved') {
        throw redirect(303, '/(app)/dashboard')
      } else {
        throw redirect(303, '/registration-status')
      }
    }
  }
  
  return {}
}

export const actions: Actions = {
  register: async ({ request, locals }) => {
    try {
      const formData = await request.formData()
      
      // Enhanced debugging for form data
      console.log('Form data keys:', [...formData.keys()])
      console.log('Raw form data:', Object.fromEntries(formData))
      
      // Extract form data - ensure values are strings
      const companyName = formData.get('company_name')?.toString() || ''
      console.log('Company Name from form:', companyName)
      
      if (!companyName || companyName.trim() === '') {
        console.error('Company name is empty or missing')
        return fail(400, { 
          error: 'Company name is required',
          formData: Object.fromEntries(formData)
        })
      }
      
      const companyData = {
        name: companyName,
        legal_entity_type: formData.get('legal_entity_type')?.toString() || '',
        tax_id: formData.get('tax_id')?.toString() || '',
        business_type: formData.get('business_type')?.toString() || '',
        estimated_annual_volume: formData.get('estimated_annual_volume')?.toString() || '',
        recruitment_enabled: formData.get('recruitment_enabled')?.toString() === 'true',
        bench_sales_enabled: formData.get('bench_sales_enabled')?.toString() === 'true',
        time_zone: formData.get('time_zone')?.toString() || 'America/New_York',
        business_address: {
          street: formData.get('street_address')?.toString() || '',
          city: formData.get('city')?.toString() || '',
          state: formData.get('state')?.toString() || '',
          zip_code: formData.get('zip_code')?.toString() || '',
          country: 'US' // US-only for now
        },
        primary_contact: {
          first_name: formData.get('first_name')?.toString() || '',
          last_name: formData.get('last_name')?.toString() || '',
          email: formData.get('email')?.toString() || '',
          phone: formData.get('phone')?.toString() || '',
          title: formData.get('title')?.toString() || ''
        },
        working_hours: {
          start: formData.get('work_start')?.toString() || '09:00',
          end: formData.get('work_end')?.toString() || '17:00'
        }
      }
      
      // Debug the processed data
      console.log('Processed company data:', JSON.stringify(companyData, null, 2))

      // Validation
      const requiredFields = [
        'company_name', 'legal_entity_type', 'tax_id', 'business_type',
        'street_address', 'city', 'state', 'zip_code',
        'first_name', 'last_name', 'email', 'phone', 'title'
      ]

      for (const field of requiredFields) {
        const value = formData.get(field)?.toString()
        console.log(`Field ${field}: ${value}`)
        
        if (!value || value.trim() === '') {
          return fail(400, { 
            error: `${field.replace(/_/g, ' ')} is required`,
            formData: Object.fromEntries(formData)
          })
        }
      }

      // Email validation
      const email = companyData.primary_contact.email
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        return fail(400, { 
          error: 'Please enter a valid email address',
          formData: Object.fromEntries(formData)
        })
      }

      // Tax ID validation (basic EIN format: XX-XXXXXXX)
      const taxId = companyData.tax_id
      const einRegex = /^\d{2}-\d{7}$/
      if (!einRegex.test(taxId)) {
        return fail(400, { 
          error: 'Tax ID must be in EIN format (XX-XXXXXXX)',
          formData: Object.fromEntries(formData)
        })
      }

      // Check if company or email already exists
      const { data: existingCompany } = await locals.supabase
        .from('companies')
        .select('id')
        .eq('name', companyData.name)
        .single()

      if (existingCompany) {
        return fail(400, { 
          error: 'A company with this name already exists',
          formData: Object.fromEntries(formData)
        })
      }

      // Check if user with this email already exists in our users table
      const { data: existingUser } = await locals.supabase
        .from('users')
        .select('id')
        .eq('email', email)
        .single()
        
      if (existingUser) {
        return fail(400, { 
          error: 'An account with this email already exists',
          formData: Object.fromEntries(formData)
        })
      }

      // Create the company record with draft status using service client to bypass RLS
      const serviceClient = createSupabaseServiceClient()
      const { data: company, error: companyError } = await serviceClient
        .from('companies')
        .insert({
          name: companyData.name,
          legal_entity_type: companyData.legal_entity_type,
          tax_id: companyData.tax_id,
          business_type: companyData.business_type,
          estimated_annual_volume: companyData.estimated_annual_volume,
          time_zone: companyData.time_zone,
          business_address: companyData.business_address,
          primary_contact: companyData.primary_contact,
          working_hours: companyData.working_hours,
          registration_status: 'draft',
          recruitment_enabled: companyData.recruitment_enabled,
          bench_sales_enabled: companyData.bench_sales_enabled,
          domain: `${companyData.name.toLowerCase().replace(/[^a-z0-9]/g, '')}.procureserve.com`,
          settings: {
            onboarding_completed: false
          }
        })
        .select()
        .single()

      if (companyError) {
        console.error('Company creation error:', companyError)
        return fail(500, { 
          error: 'Failed to create company record: ' + companyError.message,
          formData: Object.fromEntries(formData)
        })
      }

      // Create the user account
      const password = formData.get('password')?.toString() || ''
      if (!password) {
        return fail(400, {
          error: 'Password is required',
          formData: Object.fromEntries(formData)
        })
      }
      
      const { data: authUser, error: authError } = await locals.supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: companyData.primary_contact.first_name,
            last_name: companyData.primary_contact.last_name,
            company_id: company.id
          }
        }
      })

      if (authError) {
        console.error('User creation error:', authError)
        // Clean up company record using service client
        await serviceClient.from('companies').delete().eq('id', company.id)
        return fail(500, {
          error: 'Failed to create user account: ' + authError.message,
          formData: Object.fromEntries(formData)
        })
      }

      // Create user record in users table with process permissions
      const processPermissions = [];
      if (companyData.recruitment_enabled) processPermissions.push('recruitment');
      if (companyData.bench_sales_enabled) processPermissions.push('bench_sales');
      
      const { error: userError } = await locals.supabase
        .from('users')
        .insert({
          id: authUser.user!.id,
          email,
          company_id: company.id,
          role: 'admin', // Business account creator is admin
          process_permissions: processPermissions,
          current_process: processPermissions.length === 1 ? processPermissions[0] : null,
          profile: {
            first_name: companyData.primary_contact.first_name,
            last_name: companyData.primary_contact.last_name,
            phone: companyData.primary_contact.phone,
            title: companyData.primary_contact.title
          }
        })

      if (userError) {
        console.error('User record creation error:', userError)
        // Clean up company record (auth user will be cleaned up by Supabase)
        await serviceClient.from('companies').delete().eq('id', company.id)
        return fail(500, {
          error: 'Failed to create user profile: ' + userError.message,
          formData: Object.fromEntries(formData)
        })
      }

      // Redirect to document upload page
      throw redirect(303, `/register/business/documents?company_id=${company.id}`)

    } catch (error) {
      if (error instanceof Response) {
        throw error // Re-throw redirects
      }
      console.error('Registration error:', error)
      return fail(500, { 
        error: 'Registration failed. Please try again.',
        formData: Object.fromEntries(formData) // Use the already read formData
      })
    }
  }
}
