<!-- Job Creation Form - ProcureServe II -->
<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { supabase } from '$lib/supabase';
	import type { Job } from '@procureserve/shared-types';

	// Component state
	let loading = false;
	let error: string | null = null;
	let success = false;
	
	// Form data
	let job: Partial<Job> = {
		title: '',
		description: '',
		status: 'draft',
		requirements: {},
		location: '',
		remote_type: 'on_site',
		salary_min: undefined,
		salary_max: undefined,
		salary_currency: 'USD',
		salary_period: 'yearly'
	};

	// Derived properties
	$: companyId = $page.data.customerUser?.company_id;
	$: userId = $page.data.user?.id;
	$: statusOptions = getEnumValues('job_statuses');
	$: remoteOptions = getEnumValues('remote_types');
	$: salaryPeriodOptions = getEnumValues('salary_periods');
	$: currencyOptions = getEnumValues('currencies');

	// Get configurable enum values
	function getEnumValues(category: string): { key: string; label: string; color?: string }[] {
		const enums = $page.data.configurableEnums || {};
		return enums[category] || [];
	}

	// Handle form submission
	async function handleSubmit() {
		if (!companyId || !userId) {
			error = 'Session error. Please try logging in again.';
			return;
		}

		// Validate required fields
		if (!job.title || !job.description) {
			error = 'Please fill in all required fields.';
			return;
		}

		loading = true;
		error = null;
		success = false;

		try {
			// Prepare job data
			const jobData = {
				...job,
				company_id: companyId,
				created_by: userId
			};

			// Insert job
			const { data, error: insertError } = await supabase
				.from('jobs')
				.insert(jobData)
				.select()
				.single();

			if (insertError) throw insertError;

			// Success!
			success = true;
			
			// Log activity
			await supabase.from('activity_logs').insert({
				company_id: companyId,
				user_id: userId,
				entity_type: 'job',
				entity_id: data.id,
				action: 'created',
				details: {
					job_title: data.title,
					job_id: data.id,
					timestamp: new Date().toISOString()
				}
			});

			// Redirect after short delay
			setTimeout(() => {
				goto(`/jobs/${data.id}`);
			}, 1500);
		} catch (err: any) {
			console.error('Error creating job:', err);
			error = 'Failed to create job. Please try again.';
		} finally {
			loading = false;
		}
	}

	// Reset form
	function resetForm() {
		job = {
			title: '',
			description: '',
			status: 'draft',
			requirements: {},
			location: '',
			remote_type: 'on_site',
			salary_min: undefined,
			salary_max: undefined,
			salary_currency: 'USD',
			salary_period: 'yearly'
		};
		error = null;
		success = false;
	}
</script>

<div class="p-4 sm:p-6">
	<div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
		<h1 class="text-2xl font-bold">Create New Job</h1>
		<a
			href="/jobs"
			class="mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
		>
			<svg
				xmlns="http://www.w3.org/2000/svg"
				class="h-5 w-5 mr-2"
				fill="none"
				viewBox="0 0 24 24"
				stroke="currentColor"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M15 19l-7-7 7-7"
				/>
			</svg>
			Back to Jobs
		</a>
	</div>

	{#if error}
		<div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded relative mb-6">
			<strong class="font-bold">Error:</strong>
			<span class="block sm:inline">{error}</span>
		</div>
	{/if}

	{#if success}
		<div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded relative mb-6">
			<strong class="font-bold">Success!</strong>
			<span class="block sm:inline">Job created successfully. Redirecting...</span>
		</div>
	{/if}

	<div class="bg-white rounded-lg shadow overflow-hidden">
		<div class="px-6 py-4 border-b border-gray-200">
			<h2 class="text-lg font-medium">Job Details</h2>
			<p class="text-sm text-gray-500">Fill out the form to create a new job posting.</p>
		</div>

		<form on:submit|preventDefault={handleSubmit} class="p-6">
			<div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
				<!-- Job Title -->
				<div class="sm:col-span-6">
					<label for="title" class="block text-sm font-medium text-gray-700">
						Job Title <span class="text-red-500">*</span>
					</label>
					<div class="mt-1">
						<input
							type="text"
							id="title"
							bind:value={job.title}
							required
							class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
							placeholder="e.g. Senior Software Engineer"
						/>
					</div>
				</div>

				<!-- Job Description -->
				<div class="sm:col-span-6">
					<label for="description" class="block text-sm font-medium text-gray-700">
						Job Description <span class="text-red-500">*</span>
					</label>
					<div class="mt-1">
						<textarea
							id="description"
							bind:value={job.description}
							rows="6"
							required
							class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
							placeholder="Describe the job responsibilities, requirements, and any other relevant details..."
						></textarea>
					</div>
				</div>

				<!-- Location -->
				<div class="sm:col-span-3">
					<label for="location" class="block text-sm font-medium text-gray-700">
						Location
					</label>
					<div class="mt-1">
						<input
							type="text"
							id="location"
							bind:value={job.location}
							class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
							placeholder="e.g. New York, NY"
						/>
					</div>
				</div>

				<!-- Remote Type -->
				<div class="sm:col-span-3">
					<label for="remote_type" class="block text-sm font-medium text-gray-700">
						Remote Type
					</label>
					<div class="mt-1">
						<select
							id="remote_type"
							bind:value={job.remote_type}
							class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
						>
							{#each remoteOptions as option}
								<option value={option.key}>{option.label}</option>
							{/each}
						</select>
					</div>
				</div>

				<!-- Salary Range -->
				<div class="sm:col-span-6">
					<div id="salary-range-label" class="block text-sm font-medium text-gray-700 mb-2">
						Salary Range (Optional)
					</div>
					<div class="grid grid-cols-1 gap-y-4 gap-x-4 sm:grid-cols-6" aria-labelledby="salary-range-label">
						<div class="sm:col-span-2">
							<label for="salary_min" class="block text-sm font-medium text-gray-700">
								Minimum
							</label>
							<div class="mt-1">
								<input
									type="number"
									id="salary_min"
									bind:value={job.salary_min}
									min="0"
									class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
									placeholder="e.g. 80000"
								/>
							</div>
						</div>
						<div class="sm:col-span-2">
							<label for="salary_max" class="block text-sm font-medium text-gray-700">
								Maximum
							</label>
							<div class="mt-1">
								<input
									type="number"
									id="salary_max"
									bind:value={job.salary_max}
									min="0"
									class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
									placeholder="e.g. 120000"
								/>
							</div>
						</div>
						<div class="sm:col-span-1">
							<label for="salary_currency" class="block text-sm font-medium text-gray-700">
								Currency
							</label>
							<div class="mt-1">
								<select
									id="salary_currency"
									bind:value={job.salary_currency}
									class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
								>
									{#each currencyOptions as option}
										<option value={option.key}>{option.label}</option>
									{/each}
								</select>
							</div>
						</div>
						<div class="sm:col-span-1">
							<label for="salary_period" class="block text-sm font-medium text-gray-700">
								Period
							</label>
							<div class="mt-1">
								<select
									id="salary_period"
									bind:value={job.salary_period}
									class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
								>
									{#each salaryPeriodOptions as option}
										<option value={option.key}>{option.label}</option>
									{/each}
								</select>
							</div>
						</div>
					</div>
				</div>

				<!-- Job Status -->
				<div class="sm:col-span-2">
					<label for="status" class="block text-sm font-medium text-gray-700">
						Job Status
					</label>
					<div class="mt-1">
						<select
							id="status"
							bind:value={job.status}
							class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
						>
							{#each statusOptions as option}
								<option value={option.key}>{option.label}</option>
							{/each}
						</select>
					</div>
					<p class="mt-1 text-xs text-gray-500">
						Set to "Draft" to save without publishing, or "Published" to make it visible to candidates.
					</p>
				</div>

				<!-- Buttons -->
				<div class="sm:col-span-6 pt-5 flex justify-end space-x-3">
					<button
						type="button"
						on:click={resetForm}
						class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
					>
						Reset
					</button>
					<button
						type="submit"
						disabled={loading}
						class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
					>
						{#if loading}
							<svg
								class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
								xmlns="http://www.w3.org/2000/svg"
								fill="none"
								viewBox="0 0 24 24"
							>
								<circle
									class="opacity-25"
									cx="12"
									cy="12"
									r="10"
									stroke="currentColor"
									stroke-width="4"
								/>
								<path
									class="opacity-75"
									fill="currentColor"
									d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
								/>
							</svg>
							Creating...
						{:else}
							Create Job
						{/if}
					</button>
				</div>
			</div>
		</form>
	</div>
</div>
